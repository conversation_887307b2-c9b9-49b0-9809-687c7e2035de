import 'dart:convert';

import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:arabic_sign_language/presentation/core/dictionary.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';

import 'package:arabic_sign_language/bloc/Dictionary/dictionary_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../bloc/language/language_bloc.dart';
import '../../core/unity_controller.dart';

class DictionaryScreen extends StatefulWidget {
  const DictionaryScreen({super.key});

  @override
  State<DictionaryScreen> createState() => _DictionaryScreenState();
}

class _DictionaryScreenState extends State<DictionaryScreen> {
  final List<String> tabs = [
    'All Categories',
    'الحروف الأبجدية الإشارية',
    'الأرقام الإشارية',
    'اتجاهات ومواضع',
    'إدارة واقتصاد ومحاسبة',
    'صفات وحالات',
    'أشكال وألوان',
    'تعليم وتدريب',
    'بيئة وطبيعة',
    'الفضاء',
    'المنزل',
    'الأنظمة الإصلاحية والسجون',
    'الأعداد الترتيبية الوصفية',
    'الصحة والمرض',
    'باب العلاقات الاجتماعية',
    'الأسرة والمجتمع',
    'باب الضداد',
    'مرور ونقل',
    'وظائف ومهن',
    'مقاييس وأزمنة',
    'وطني',
    'مصطلحات متنوعة',
    'باب الدين',
    'التربية الاجتماعية',
    'الحاسوب',
    'العلوم',
    'الرياضة و الفنون',
    'دول العالم ومدنها',
    'طيران ناس',
    'اللغة العربية',
    'الفنية',
    'رياضيات',
    'التربية الأسرية',
    'الكلمات الشائعة',
    'أكاديمية',
    'مصطلحات في الصحة',
  ];
  final TextEditingController searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  UnityWidgetController? _unityWidgetController;
  List<String> currentProcessingTexts = [];
  ValueNotifier<int> currentIndex = ValueNotifier<int>(0);
  List<String> messagesFromUnity = [];
  List<String> currentMessageList = [];

  void onUnityCreated(controller) {
    _unityWidgetController = controller;
    setCurrentUnityController(controller);
  }

  Future<void> sendMessageToUnity(Map<String, dynamic> message) async {
    message['screen'] = "DictionaryScreen";

    String jsonString = json.encode(message);
    debugPrint("jsonString =>$jsonString");
    await _unityWidgetController?.postMessage(
        'SaudiCharacter', 'PlaySignAnim', jsonString);
  }

  Future<void> stopAnimations() async {
    if (_unityWidgetController != null) {
      await _unityWidgetController?.postMessage(
          'SaudiCharacter', 'StopAnimations', "");
    }
  }

  Future<void> adjustAnimationSpeed(String value) async {
    await _unityWidgetController?.postMessage(
        "SaudiCharacter", 'SetAnimationSpeed', value);
  }

  void pushToSecond(String value) {
    currentProcessingTexts.add(value); // Add the value to `second`.

    // Combine and clean up the processing texts
    String combined = currentProcessingTexts.join().replaceAll(' ', '');
    print("Combined value: $combined");

    // Safety check: Ensure rootWords is not empty and currentIndex is valid
    final rootWords = context.read<DictionaryBloc>().rootWords;
    if (rootWords.isEmpty ||
        currentIndex.value >= rootWords.length ||
        currentIndex.value < 0) {
      print(
          "Error: Invalid rootWords access. rootWords length: ${rootWords.length}, currentIndex: ${currentIndex.value}");
      return; // Exit early to prevent crash
    }

    String currentFirstElement =
        rootWords[currentIndex.value].replaceAll(',', '').replaceAll(' ', '');
    print("Current first element: $currentFirstElement ");
    print("Current first element: $combined");

    print("Current first element: $rootWords => ${currentIndex.value}");
    print("Current first element: $value => $currentProcessingTexts");
    print("Current index before update: ${currentIndex.value}");

    // Matching logic
    if (combined == currentFirstElement) {
      currentIndex.value += 1; // Move to the next element in `first`.
      print(
          "Current index after update: ${currentIndex.value}, $currentProcessingTexts");
      currentProcessingTexts.clear(); // Clear for the next processing
      print("Current index after update:  => $currentProcessingTexts");

      // Additional safety check: Reset index if it goes out of bounds
      if (currentIndex.value >= rootWords.length) {
        print(
            "Warning: currentIndex exceeded rootWords length. Resetting to 0.");
        currentIndex.value = 0;
      }
    } else if (combined.length > currentFirstElement.length) {
      print("Warning: Combined value exceeds the current `first` element.");
    } else {
      print("Currently processing index ${currentIndex.value}");
    }
  }

  // Communication from Unity to Flutter
  Future<void> onUnityMessage(message) async {
    if (!mounted)
      return; // Add this check to prevent accessing context after disposal

    if (message.toString().contains('Current playing Animation')) {
      List<String> parts = message.split('&');
      Map<String, String> result = {};
      for (var part in parts) {
        List<String> keyValue =
            part.split('=>').map((str) => str.trim()).toList();
        if (keyValue.length == 2) {
          result[keyValue[0]] = keyValue[1];
        }
      }
      // final data = splitWords(message.toString());
      // String combinedData = result.join('  ');
      if (result["screen"] == "DictionaryScreen") {
        print(
            "currentAnimation unityScreen => ${result['Current playing Animation']}");
        if (mounted) {
          // Add mounted check before accessing context
          context.read<DictionaryBloc>().add(UpdateCurrentAnimationText(
              animationText: result['Current playing Animation'] ?? ""));
        }
      }
    } else if (message.toString().contains('switchtoIdle')) {
      Future.delayed(const Duration(seconds: 3), () {
        print("UpdateCurrentAnimationText => 3s");
        if (mounted) {
          // Add mounted check before accessing context
          context
              .read<DictionaryBloc>()
              .add(const UpdateCurrentAnimationText(animationText: ""));
        }
        // context.read<UnityScreenBloc>().isAnimating = false;
        // currentIndex.value = 0;
        // context.read<UnityScreenBloc>().rootWords.clear();
      });
    } else if (message.toString().contains("Current Animation")) {
      currentMessageList.add(message.toString().split("=>").last);
      print("startSpeechToTextProcessing =>c$currentMessageList");
      if (mounted) {
        // Add mounted check before calling pushToSecond
        pushToSecond(message.toString().split("=>").last);
      }
      if (messagesFromUnity.length == currentMessageList.length && mounted) {
        currentIndex.value = 0;
        messagesFromUnity.clear();
        currentMessageList.clear();
        if (mounted) {
          // Add mounted check before accessing context
          context.read<DictionaryBloc>().rootWords.clear();
          context
              .read<DictionaryBloc>()
              .add(const UpdateCurrentAnimationText(animationText: ""));
          print(
              "onUnityMessage => ${context.read<DictionaryBloc>().speechTexts}");
          context.read<DictionaryBloc>().speechTextIndex++;
        }
      }
    }
  }

  List<String> splitWords(String message) {
    const prefix = "Current playing Animation =>";
    if (message.startsWith(prefix)) {
      String words = message.substring(prefix.length).trim();
      return words.split(' ');
    }
    return [];
  }

  @override
  void dispose() {
    // Stop any ongoing animations
    stopAnimations();

    // Dispose controllers
    searchController.dispose();
    _scrollController.dispose();
    currentIndex.dispose();

    // Clear lists
    messagesFromUnity.clear();
    currentMessageList.clear();
    currentProcessingTexts.clear();
    if (currentUnityController == _unityWidgetController) {
      currentUnityController = null;
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: BlocConsumer<DictionaryBloc, DictionaryState>(
        listener: (context, state) {
          print("state from dictionary screen => $state");
          if (state is DictionaryItemSelected) {
            messagesFromUnity.add(state.message['root']);
            sendMessageToUnity(state.message);
          }
        },
        builder: (context, state) {
          return Stack(
            children: [
              Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage(APP_BG), fit: BoxFit.cover),
                ),
              ),

              // Main Content Area (excluding bottom nav space)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: 95, // Reserve space for bottom nav
                child: Column(
                  children: [
                    // Fixed Header and Tab Bar
                    Container(
                      padding:
                          const EdgeInsets.fromLTRB(0, kToolbarHeight, 0, 0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildHeader(context),
                          const SizedBox(height: 15),
                          BlocBuilder<DictionaryBloc, DictionaryState>(
                            builder: (context, state) {
                              return _buildTabBar();
                            },
                          ),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),

                    // Scrollable Content Area
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 20),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          gradient: LinearGradient(
                            colors: [
                              const Color(0XFF9064FC).withOpacity(0.5),
                              const Color(0XFF1E113A).withOpacity(0.8),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                        child: BlocBuilder<DictionaryBloc, DictionaryState>(
                          builder: (context, state) {
                            return Column(
                              children: [
                                // Character Avatar (now fixed)
                                const SizedBox(height: 10),
                                _buildCharacterAvatar(context),
                                const SizedBox(height: 20),
                                _buildStatusText(
                                    state, MediaQuery.sizeOf(context)),

                                // Scrollable content
                                Expanded(
                                  child: SingleChildScrollView(
                                    controller: _scrollController,
                                    padding: EdgeInsets.fromLTRB(
                                      0,
                                      15,
                                      0,
                                      MediaQuery.of(context).viewInsets.bottom +
                                          20,
                                    ),
                                    child: Column(
                                      children: [
                                        if (state.selectedTabIndex == 0) ...[
                                          _buildQuickPhrasesSection(context),
                                          const SizedBox(height: 20),
                                        ],
                                        SizedBox(
                                          height: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.3,
                                          child: _buildTabSpecificContent(
                                            state.selectedTabIndex,
                                            state.searchQuery,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          // Back button
          GestureDetector(
              onTap: () {
                bottomBarIndex.value = 0;
                stopAnimations();
              },
              child: Image.asset(BACK_BUTTON, height: 25, width: 25)),

          const SizedBox(width: 15),

          // Title
          Text(
            'sign_language_dictionary'.tr(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: FONT_FAMILY,
            ),
          ),

          const Spacer(),

          // Search button
          GestureDetector(
              onTap: () => _showSearchBottomSheet(context),
              child: const Icon(
                Icons.search,
                color: Colors.white,
                size: 25,
              )),

          const SizedBox(width: 10),

          // Language toggle button
          // BlocBuilder<LanguageBloc, LanguageState>(
          //   builder: (context, state) {
          //     return GestureDetector(
          //       onTap: () {
          //         // Toggle language
          //         context.read<LanguageBloc>().add(ToggleLanguage());

          //         // Set locale based on current language
          //         final newLocale = state.locale.languageCode == 'en'
          //             ? const Locale('ar')
          //             : const Locale('en');

          //         context.setLocale(newLocale);

          //         // Force rebuild by triggering a state change
          //         setState(() {});
          //       },
          //       child: Container(
          //         padding: const EdgeInsets.all(8),
          //         decoration: BoxDecoration(
          //           color: Colors.white.withOpacity(0.1),
          //           borderRadius: BorderRadius.circular(20),
          //         ),
          //         child: Text(
          //           state.locale.languageCode == 'en' ? 'AR' : 'EN',
          //           style: const TextStyle(
          //             color: Colors.white,
          //             fontSize: 14,
          //             fontWeight: FontWeight.w500,
          //           ),
          //         ),
          //       ),
          //     );
          //   },
          // ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return BlocBuilder<DictionaryBloc, DictionaryState>(
      builder: (context, state) {
        return Container(
          height: 45, // Fixed height for the tab bar
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            itemCount: tabs.length,
            itemBuilder: (context, index) {
              return _buildTabItem(index, state.selectedTabIndex);
            },
          ),
        );
      },
    );
  }

  Widget _buildTabItem(int index, int selectedTabIndex) {
    final isSelected = selectedTabIndex == index;
    return Container(
      margin: const EdgeInsets.only(right: 15), // Add margin between tabs
      child: GestureDetector(
        onTap: () {
          context.read<DictionaryBloc>().add(ChangeTab(tabIndex: index));
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color:
                    isSelected ? const Color(0XFF7B6FFF) : Colors.transparent,
                width: 2,
              ),
            ),
          ),
          child: Text(
            tabs[index],
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.6),
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.w300,
              fontSize: 12,
              fontFamily: FONT_FAMILY_INTER,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabSpecificContent(int tabIndex, String searchQuery) {
    // Get dictionary data and convert to list format
    List<Map<String, dynamic>> dictionaryItems = dictionary.entries
        .map((entry) => {
              'title': entry.key,
              'synonyms': entry.value,
            })
        .toList();

    // Filter data based on selected tab
    List<Map<String, dynamic>> filteredItems = dictionaryItems;

    if (tabIndex == 0) {
      // "All Categories" tab - no additional filtering
    } else {
      // Filter by category
      String category = tabs[tabIndex];
      // Here you would implement category-based filtering
      // This is a placeholder - you'll need to adjust based on your data structure
      filteredItems = dictionaryItems.where((item) {
        // Assuming each item has a 'category' field or you have some way to map items to categories
        // This is just an example - adjust according to your actual data structure
        return item['category'] == category ||
            (item['synonyms'] as List<String>).contains(category);
      }).toList();
    }

    // Further filter based on search query (only for "All Categories" tab)
    if (tabIndex == 0 && searchQuery.isNotEmpty) {
      filteredItems = filteredItems.where((item) {
        final title = item['title']!.toString().toLowerCase();
        final synonyms =
            (item['synonyms'] as List<String>).join(' ').toLowerCase();
        return title.contains(searchQuery.toLowerCase()) ||
            synonyms.contains(searchQuery.toLowerCase());
      }).toList();
    }

    // Show "No Data Found" if no items match search query or category
    if (filteredItems.isEmpty) {
      return Center(
        child: Text(
          'no_data_found'.tr(),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w500,
            fontFamily: FONT_FAMILY,
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: ListView.builder(
        padding: const EdgeInsets.all(0),
        itemCount: filteredItems.length,
        itemBuilder: (context, index) {
          final item = filteredItems[index];
          final title = item['title']!.toString();
          final synonyms = item['synonyms'] as List<String>;
          final subtitle = synonyms.isNotEmpty && synonyms.first.isNotEmpty
              ? synonyms.first
              : 'Dictionary';

          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: FONT_FAMILY,
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    context.read<DictionaryBloc>().add(
                          SelectDictionaryItem(item: title),
                        );
                    // Send message to Unity for animation
                    // _sendToUnity(title);

                    // Scroll to top with animation
                    // _scrollToTop();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCharacterAvatar(BuildContext context) {
    final screenHeight = MediaQuery.sizeOf(context).height;
    return Container(
      height: screenHeight * 0.25, // Increased height for better proportions
      margin: const EdgeInsets.symmetric(horizontal: 40),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white.withOpacity(0.1),
            Colors.white.withOpacity(0.05),
          ],
        ),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Character image in ClipRRect for rounded corners
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: UnityWidget(
              onUnityCreated: onUnityCreated,
              onUnityMessage: onUnityMessage,
            ),
          ),

          // Control buttons positioned to the right
          // Positioned(
          //   right: -25, // Position outside the container
          //   top: screenHeight * 0.05, // Center vertically
          //   child: Container(
          //     decoration: BoxDecoration(
          //       borderRadius: BorderRadius.circular(23),
          //       color: const Color(0XFF49446C).withOpacity(0.7),
          //     ),
          //     padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
          //     child: Column(
          //       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          //       children: [
          //         _buildControlButton(
          //             Icons.refresh, () {}, const Color(0XFF7B6FFF)),
          //         const SizedBox(height: 10),
          //         _buildControlButton(
          //             Icons.add, () {}, const Color(0XFF2D2456)),
          //         const SizedBox(height: 10),
          //         _buildControlButton(Icons.remove, () {},
          //             const Color.fromARGB(255, 54, 53, 61)),
          //       ],
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildQuickPhrasesSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.bottomRight,
            child: Text(
              'quick_phrases'.tr(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
                fontFamily: FONT_FAMILY_INTER,
              ),
              textAlign: TextAlign.right,
            ),
          ),

          // First row of buttons
          Row(
            children: [
              Expanded(
                child: _buildQuickPhraseButton('شيء', () {
                  _sendToUnity('شيء');
                  // _scrollToTop();
                }),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: _buildQuickPhraseButton('شكرا', () {
                  _sendToUnity('شكرا');
                  // _scrollToTop();
                }),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: _buildQuickPhraseButton('صافية', () {
                  _sendToUnity('صافية');
                  // _scrollToTop();
                }),
              ),
            ],
          ),
          const SizedBox(height: 10),

          // Second row of buttons
          Row(
            children: [
              Expanded(
                child: _buildQuickPhraseButton('آسف', () {
                  _sendToUnity('آسف');
                  // _scrollToTop();
                }),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: _buildQuickPhraseButton('صباح', () {
                  _sendToUnity('صباح');
                  // _scrollToTop();
                }),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: _buildQuickPhraseButton('لا', () {
                  _sendToUnity('لا');
                  // _scrollToTop();
                }),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickPhraseButton(String text, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontFamily: FONT_FAMILY,
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
        ),
      ),
      child: ValueListenableBuilder<TextEditingValue>(
        valueListenable: searchController,
        builder: (context, value, child) {
          return TextField(
            controller: searchController,
            onTap: () {
              // Select all text when tapping on the field if it has content
              if (searchController.text.isNotEmpty) {
                searchController.selection = TextSelection(
                  baseOffset: 0,
                  extentOffset: searchController.text.length,
                );
              }
            },
            onChanged: (value) {
              context
                  .read<DictionaryBloc>()
                  .add(SearchDictionary(query: value));
            },
            style: const TextStyle(
              color: Colors.white,
              fontFamily: FONT_FAMILY,
            ),
            decoration: InputDecoration(
              hintText: 'search_for_sign'.tr(),
              hintStyle: const TextStyle(
                color: Colors.white54,
                fontFamily: FONT_FAMILY,
              ),
              border: InputBorder.none,
              prefixIcon: const Icon(
                Icons.search,
                color: Colors.white54,
              ),
              // Add clear button when there's text
              suffixIcon: value.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(
                        Icons.clear,
                        color: Colors.white54,
                      ),
                      onPressed: () {
                        searchController.clear();
                        Navigator.of
                        context.read<DictionaryBloc>().add(
                              const SearchDictionary(query: ''),
                            );
                      },
                    )
                  : null,
            ),
          );
        },
      ),
    );
  }

  Widget _buildControlButton(
      IconData icon, VoidCallback onTap, Color backgroundColor) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
  }

  void _sendToUnity(String word) {
    // Send message to Unity for sign language animation
    context.read<DictionaryBloc>().add(
          SelectDictionaryItem(item: word),
        );

    // Scroll to top with animation
    // _scrollToTop();
  }

  Widget _buildStatusText(DictionaryState state, Size size) {
    if (!mounted) return const SizedBox.shrink();

    return Container(
      width: MediaQuery.sizeOf(context).width,
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: BlocBuilder<DictionaryBloc, DictionaryState>(
        builder: (context, state) {
          List<String> words = state.currentAnimation
              .replaceAll('[', '')
              .replaceAll(']', '')
              .split(', ');
          return SizedBox(
            width: size.width * 0.7,
            child: ValueListenableBuilder(
                valueListenable: currentIndex,
                builder: (context, currentActiveIndex, _) {
                  return Text.rich(
                    TextSpan(
                      children: List<TextSpan>.generate(words.length, (index) {
                        return TextSpan(
                          text:
                              '${words[index]}${index < words.length - 1 ? ' ' : ''}', // Add space between words
                          style: TextStyle(
                            backgroundColor: index == currentActiveIndex
                                ? const Color(0XFF755BFF)
                                : null,
                            color: Colors.white, // Highlighted color
                            fontWeight: FontWeight.bold, // Bold for highlighted
                            fontSize: 18,
                          ),
                        );
                      }),
                    ),
                    textAlign: TextAlign.center,
                  );
                }),
          );
        },
      ),
    );
  }

  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showSearchBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0XFF9064FC),
              Color(0XFF1E113A),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 10),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Text(
                    'search_dictionary'.tr(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: FONT_FAMILY,
                    ),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),

            // Search bar
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildSearchBar(context),
            ),

            const SizedBox(height: 20),

            // Search results
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                child: BlocBuilder<DictionaryBloc, DictionaryState>(
                  builder: (context, state) {
                    return _buildTabSpecificContent(0, state.searchQuery);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
