import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:arabic_sign_language/data/models/audio/audio_response.dart';
import 'package:arabic_sign_language/data/models/recording_model/recording_model.dart';
import 'package:arabic_sign_language/data/service/recoding_service.dart';
import 'package:arabic_sign_language/data/service/text_conversion_service.dart';
import 'package:asl_flutter_input/asl_flutter_input.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_to_text.dart';

import '../../data/models/audio/upload_session_response.dart';

part 'speech_transcript_event.dart';
part 'speech_transcript_state.dart';

class SpeechTranscriptBloc
    extends Bloc<SpeechTranscriptEvent, SpeechTranscriptState> {
  final RecordingService recordingService;
  final TextConversionService textConversionService;
  final _aslFlutterInputPlugin = AslFlutterInput();
  List<String> rootWords = [];
  int speechTextIndex = 0;
  List<List<String>> speechTexts = [];
  List<List<String>> speechWords = [];

  // Speech recognition properties
  String recognizedWord = "";
  String lastResponse = "";
  String currentResponse = '';
  String previousResult = "";
  String newTextToProcess = "";
  Timer? _speechTimer;
  String lastProcessedText = "";

  Timer? _androidSpeechTimer;
  String lastUpdatedText = '';
  int noResponseCount = 0;
  bool isAnimating = false;
  bool _shouldStopAnimationProcesses = false;
  double animationSpeed = 1.0;

  Timer? audioChunkTimer;
  List<AudioSegment> audioResponseList = [];
  bool isChunkUploading = false;
  bool isChunkDataProcessing = false;
  int audioSegmentIndex = 0;

  List<RecordingModel> latsRecordingList = [];
  List<RecordingModel> lastFileUploadList = [];

  Timer? _autoStopTimer;
  Timer? sessionTimer;
  Timer? popupCountdownTimer;
  bool isPopupVisible = false;
  int alertCount = 0;
  String recorderPath = '';

  SpeechTranscriptBloc({
    required this.recordingService,
    required this.textConversionService,
  }) : super(const SpeechTranscriptInitial()) {
    on<StartRecording>(_onStartRecording);
    on<StopRecording>(_onStopRecording);
    on<ResetSpeechTranscription>(_onResetSpeechTranscription);
    on<TranslateButtonTapped>(_onTranslateButtonTapped);
    on<UpdateCurrentAnimationText>(_onUpdateCurrentAnimationText);
    on<StartSpeechRecognition>(_onStartSpeechRecognition);
    on<StopSpeechRecognition>(_onStopSpeechRecognition);
    on<RestartSpeechRecognition>(_onRestartSpeechRecognition);
    on<UploadFileForTranscription>(_onUploadFileForTranscription);
    on<StopAllAnimationProcesses>(_onStopAllAnimationProcesses);
    on<UpdateAnimationSpeed>(_onUpdateAnimationSpeed);
    on<UpdateSliderValue>(_onUpdateSliderValue);
    on<ReplayTranscription>(_onReplayTranscription);
    on<SendUpdatedSpeech>(_onSendUpdatedSpeech);
    on<UploadChunkData>(_onUploadChunkData);
    on<SendAudioSegment>(_onSendAudioSegment);
    on<ReplayRecording>(_onReplayRecording);
    on<ReplayFileUpload>(_onReplayFileUpload);
    on<SendSessionAlert>(_onSendSessionAlert);
    on<UploadRecording>(_onUploadRecording);
  }

  FutureOr<void> _onStartRecording(
      StartRecording event, Emitter<SpeechTranscriptState> emit) async {
    emit(StartRecordingState(
      isRecordingEnabled: event.isRecordingEnabled,
      animationSpeed: state.animationSpeed,
    ));
    final hasPermission = await event.controller.checkPermission();
    if (hasPermission) {
      await event.controller.record(path: event.path);
      await FirebaseAnalytics.instance.logEvent(
          name: "onStartRecording",
          parameters: {
            "status": "Completed",
            "timeStamp": DateTime.now().toString()
          });
      // Cancel any previous timers
      _autoStopTimer?.cancel();

      // Schedule automatic stop after 2 minutes
      _autoStopTimer = Timer(const Duration(minutes: 2), () {
        if (event.controller.isRecording) {
          add(StopRecording(
            isRecordingEnabled: false,
            controller: event.controller,
            path: event.path,
          ));
        }
      });
    } else {
      emit(SpeechScreenError(
          message: "Permission denied", animationSpeed: state.animationSpeed));
    }
  }

  FutureOr<void> _onStopRecording(
      StopRecording event, Emitter<SpeechTranscriptState> emit) async {
    _autoStopTimer?.cancel();
    emit(StopRecordingState(
      isRecordingStopped: true,
      isRecordingEnabled: state.isRecordingEnabled,
      animationSpeed: state.animationSpeed,
    ));

    await event.controller.pause();
  }

  FutureOr<void> _onResetSpeechTranscription(
      ResetSpeechTranscription event, Emitter<SpeechTranscriptState> emit) {
    event.controller.stop();
    emit(const SpeechTranscriptInitial());
  }

  FutureOr<void> _onTranslateButtonTapped(
      TranslateButtonTapped event, Emitter<SpeechTranscriptState> emit) async {
    emit(StartRecordingState(
        isRecordingEnabled: true,
        animationSpeed: state.animationSpeed,
        isDataLoading: true));
    final path = await event.controller.stop();
    if (path?.isNotEmpty == true) {
      final recording = await File(path!).readAsBytes();

      final fileName = path.split('/').last;
      print("Recording file name: $fileName");

      final recorderList = await recordingService.getTranscriptionFromRecording(
        recording,
        event.sourceType,
        fileName: fileName,
      );
      latsRecordingList = recorderList;
      await FirebaseAnalytics.instance.logEvent(
          name: "onTranslateButtonTapped",
          parameters: {
            "status": "Completed",
            "timeStamp": DateTime.now().toString()
          });
      print("recorderList => $recorderList");

      if (recorderList.isEmpty) {
        emit(SpeechScreenError(
            message: "Please try again", animationSpeed: state.animationSpeed));
        // emit(const UnityScreenError(
        //   message: "Please try again",
        //   isSpeechButtonDisabled: false,
        //   isVideoButtonDisabled: false,
        // ));
      } else {
// for (var textItem in texts) {
//         final rootWord = textItem.root;
//         for (var item in rootWord) {
//           rootWords.add(item);
//           if (item.contains(',')) {
//             final splitWords = item.split(',');
//             for (int i = 0; i < splitWords.length; i++) {
//               print(
//                   "Iteration: $i, Word: ${splitWords[i]}, Full List: $splitWords");
//               await Future.delayed(const Duration(milliseconds: 150), () {
//                 emit(SendMessagesToUnity(

        for (var item in recorderList) {
          final rootWord = item.root ?? "";
          // rootWords.add(rootWord);
          if (rootWord.contains(',')) {
            final splitWords = rootWord.split(',');
            for (var word in splitWords) {
              emit(SendMessagesToSpeechScreen(
                message: {'root': word, 'word': item.word},
                currentAnimation: state.currentAnimation,
                animationSpeed: state.animationSpeed,
              ));
              // emit(SendMessagesToUnity(
              //   message: {'root': word, 'word': item.word},
              //   currentAnimation: state.currentAnimation,
              // ));
            }
          } else {
            emit(SendMessagesToSpeechScreen(
              message: {'root': item.root, 'word': item.word},
              currentAnimation: state.currentAnimation,
              animationSpeed: state.animationSpeed,
            ));
            // emit(SendMessagesToUnity(
            //   message: {'root': item.root, 'word': item.word},
            //   currentAnimation: state.currentAnimation,
            // ));
          }
        }
      }
    } else {
      emit(SpeechScreenError(
          message: "Failed to stop recording",
          animationSpeed: state.animationSpeed));
    }
    event.controller.refresh();
  }

  FutureOr<void> _onUpdateCurrentAnimationText(UpdateCurrentAnimationText event,
      Emitter<SpeechTranscriptState> emit) async {
    emit(UpdateCurrentAnimation(
        currentAnimation: event.animationText,
        isRecordingEnabled: state.isRecordingEnabled,
        isTranslateButtonDisabled: state.isTranslateButtonDisabled,
        isDataLoading: state.isDataLoading,
        isRecordingStopped: state.isRecordingStopped,
        animationSpeed: state.animationSpeed));
  }

  FutureOr<void> _onStartSpeechRecognition(
    StartSpeechRecognition event,
    Emitter<SpeechTranscriptState> emit,
  ) async {
    recognizedWord = "";
    lastResponse = "";
    currentResponse = '';
    previousResult = ""; // Store the previous result
    newTextToProcess = "";
    // emit(const StartSpeechToText(
    //   isTextToSpeechStart: true,
    //   isRecorderButtonDisabled: true,
    //   isVideoButtonDisabled: true,
    //   isDictionaryButtonDisabled: true,
    //   isTextFormFieldDisabled: true,
    // ));
    if (Platform.isIOS) {
      // Await initialization
      bool initialized = await event.speechToText.initialize(
        onError: (errorNotification) {
          recognizedWord = "";
        },
        onStatus: (status) async {
          if (Platform.isIOS) {
            _speechTimer = Timer.periodic(const Duration(seconds: 5), (_) {
              if (status == "listening" && recognizedWord.isNotEmpty) {
                if (recognizedWord.isEmpty) {
                  recognizedWord = '';
                }
                _processLatestText(recognizedWord);
              }
            });
          } else {
            if (status == "done") {
              // add(RestartSpeechRecognisation(words: recognizedWord));
              await startListing(event.speechToText, emit);
              recognizedWord = '';
            }
          }
        },
      );

      if (initialized) {
        await startListing(event.speechToText, emit);
      } else {
        // emit(const StartSpeechToText(isTextToSpeechStart: false));
        // emit(const UnityScreenError(message: "Permission Denied"));
      }
    } else {
      final status = await _reqAudioPermission();
      if (status == PermissionStatus.granted) {
        _startListening();
      }
    }
  }

  FutureOr<void> _onStopSpeechRecognition(
    StopSpeechRecognition event,
    Emitter<SpeechTranscriptState> emit,
  ) {
    if (Platform.isIOS) {
      event.speechToText.stop();
      _speechTimer?.cancel;
      recognizedWord = '';
      lastResponse = "";
    } else {
      _aslFlutterInputPlugin.stopListening();
      _androidSpeechTimer?.cancel();
      recognizedWord = '';
      lastResponse = "";
    }
    // emit(const StartSpeechToText(isTextToSpeechStart: false));
    speechTexts.clear();
    speechWords.clear();
    speechTextIndex = 0;
    isAnimating = false;
    noResponseCount = 0;
    lastUpdatedText = "";
    sessionTimer?.cancel();
    popupCountdownTimer?.cancel();
    isPopupVisible = false;
    alertCount = 0;
  }

  FutureOr<void> _onRestartSpeechRecognition(
    RestartSpeechRecognition event,
    Emitter<SpeechTranscriptState> emit,
  ) async {
    if (event.words.isNotEmpty && !emit.isDone) {
      try {
        final texts = await textConversionService.getTranscribedText(
            event.words, "live_translation");
        if (texts.isNotEmpty && !emit.isDone) {
          for (var textItem in texts) {
            final rootWord = textItem.root;
            final speechWord = textItem.word;
            speechTexts.add(rootWord);
            speechWords.add(speechWord);
            if (!isAnimating) {
              for (var item in speechTexts[speechTextIndex]) {
                rootWords.add(item);
                print("startSpeechToTextProcessing =>rootWords $rootWords");
                if (item.contains(',')) {
                  final splitWords = item.split(',');

                  for (int i = 0; i < splitWords.length; i++) {
                    var word = splitWords[i];
                    print("speechTextItem =>$word");
                    await Future.delayed(const Duration(milliseconds: 150), () {
                      emit(SendMessagesToSpeechScreen(
                        message: {
                          'root': word,
                          'word': textItem.word.toString()
                        },
                        currentAnimation: state.currentAnimation,
                        animationSpeed: state.animationSpeed,
                        // isTextToSpeechStart: state.isTextToSpeechStart,
                        // isVideoButtonDisabled: state.isVideoButtonDisabled,
                        // isRecorderButtonDisabled: state.isRecorderButtonDisabled,
                        // isDictionaryButtonDisabled:
                        //     state.isDictionaryButtonDisabled,
                        // isTextFormFieldDisabled: state.isTextFormFieldDisabled,
                      ));
                    });
                  }
                } else {
                  emit(SendMessagesToSpeechScreen(
                    message: {'root': item, 'word': textItem.word.toString()},
                    currentAnimation: state.currentAnimation,
                    animationSpeed: state.animationSpeed,
                    // isTextToSpeechStart: state.isTextToSpeechStart,
                    // isVideoButtonDisabled: state.isVideoButtonDisabled,
                    // isRecorderButtonDisabled: state.isRecorderButtonDisabled,
                    // isDictionaryButtonDisabled: state.isDictionaryButtonDisabled,
                    // isTextFormFieldDisabled: state.isTextFormFieldDisabled,
                  ));
                }
              }
            }
          }
        } else {
          if (Platform.isIOS) {
            _speechTimer?.cancel();
          } else {
            _androidSpeechTimer?.cancel();
          }
          // emit(SpeechScreenError(
          //     message: "Failed to process speech",
          //     animationSpeed: state.animationSpeed));
        }
      } catch (e) {
        print("SpeechTranscriptBloc: Error in _onRestartSpeechRecognition: $e");
        if (!emit.isDone) {
          emit(SpeechScreenError(
              message: "Failed to process speech",
              animationSpeed: state.animationSpeed));
        }
      }
    }
  }

  Future<void> _startListening() async {
    try {
      await _setAPIKey();
      await FirebaseAnalytics.instance.logEvent(
          name: "startListening",
          parameters: {
            "status": "Completed",
            "timeStamp": DateTime.now().toString()
          });
      // await _aslFlutterInputPlugin.setCurrentLanguage('ar-SA');
      await _aslFlutterInputPlugin.initAudioTranscription();
      await _startTimer();
    } on Exception catch (e) {
      debugPrint('[Exception][_startListening]: $e');
    }
  }

  // Android permission request method - same as Unity
  Future<PermissionStatus> _reqAudioPermission() async {
    return await Permission.microphone.request();
  }

  // void _processRecognizedText(String text) {
  //   if (text.isNotEmpty && text != lastResponse) {
  //     lastResponse = text;
  //     add(RestartSpeechRecognition(words: text));
  //   }
  // }

  void _processLatestText(String newText) {
    if (newText.isNotEmpty && newText != lastProcessedText) {
      String latestPart = '';

      // Find the index where the new text differs from the old text
      int diffIndex = 0;
      while (diffIndex < newText.length &&
          diffIndex < lastProcessedText.length &&
          newText[diffIndex] == lastProcessedText[diffIndex]) {
        diffIndex++;
      }

      // Extract only the new part of the text
      latestPart = newText.substring(diffIndex);

      print('Current Response => $newText');
      print('New text to process: $latestPart');

      // Process the latest part of the text
      _handleLatestText(latestPart);

      // Update the last processed text
      lastProcessedText = newText;
      add(RestartSpeechRecognition(words: latestPart));
    }
  }

  void _handleLatestText(String text) {
    // Implement your processing logic here
    print('Processing latest part: $text');
  }

  Future<void> startListing(
    SpeechToText speech,
    Emitter<SpeechTranscriptState> emit,
  ) async {
    await speech.listen(
      onResult: (res) {
        print("res=> ${res.recognizedWords}");
        recognizedWord = res.recognizedWords;
      },
      localeId: "ar-SA",
    );
  }

  Future<void> _setAPIKey() async {
    try {
      final api = await _aslFlutterInputPlugin
          .setAPIKey("AIzaSyCizaPQJGhorO8m00L3uBuJzX3H5hVm_2c");
      print("_setAPIKey => $api");
    } catch (e) {
      print("Error =>  _setAPIKey => $e");
    }
  }

  _startTimer() async {
    _androidSpeechTimer = Timer.periodic(const Duration(seconds: 5), (_) async {
      recognizedWord = await _aslFlutterInputPlugin.fetchTranscribedText();
      print("Current Response =>recognizedWord $recognizedWord ");
      if (recognizedWord.contains('؟')) {
        recognizedWord = recognizedWord.replaceAll('؟', '');
      }
      if (recognizedWord.isEmpty) {
        print("No transcription received.");
        recognizedWord = ''; // Skip processing if no text was recognized
      } else {
        // print("New text to process: ${_processLatestText(recognizedWord)}");
        if (lastUpdatedText != recognizedWord) {
          lastUpdatedText = recognizedWord;
          noResponseCount = 0;
        } else {
          noResponseCount = noResponseCount + 1;
          if (noResponseCount >= 10) {
            isAnimating = false;
          }
        }
        _processLatestText(recognizedWord);
      }
    });
    startSessionTimer();
  }

  void startSessionTimer() {
    sessionTimer?.cancel();
    sessionTimer = Timer(const Duration(minutes: 5), () {
      alertCount++;
      add(SendSessionAlert(alertCount: alertCount));
    });
  }

  FutureOr<void> _onUploadFileForTranscription(UploadFileForTranscription event,
      Emitter<SpeechTranscriptState> emit) async {
    try {
      // Reset cancellation flag for new process
      _shouldStopAnimationProcesses = false;

      // Emit loading state to show progress indicator
      emit(FileUploadLoadingState(animationSpeed: state.animationSpeed));

      // Call the recording service to get transcription from uploaded file
      final recorderList = await recordingService.getTranscriptionFromRecording(
        event.fileBytes,
        event.sourceType,
        progressNotifier: event.progressNotifier,
        fileName: event.fileName,
      );
      lastFileUploadList = recorderList;
      await FirebaseAnalytics.instance.logEvent(
          name: "onUploadFileForTranscription",
          parameters: {
            "status": "Completed",
            "timeStamp": DateTime.now().toString()
          });

      if (recorderList.isEmpty) {
        event.progressNotifier.value = 0.0;
        emit(SpeechScreenError(
            message: "No transcription found. Please try again.",
            animationSpeed: state.animationSpeed));
        return;
      }

      // Process the transcription results similar to TranslateButtonTapped
      emit(const SpeechTranscriptInitial(
        isRecordingEnabled: false,
        isTranslateButtonDisabled: true,
        isDataLoading: false,
      ));
      event.progressNotifier.value = 0.0;

      // Send messages to Unity for each transcription result
      for (var item in recorderList) {
        // Check if we should stop the animation processes
        if (_shouldStopAnimationProcesses) {
          break;
        }

        final rootWord = item.root ?? "";
        if (rootWord.contains(',')) {
          final splitWords = rootWord.split(',');
          for (var word in splitWords) {
            // Check again before emitting
            if (_shouldStopAnimationProcesses) {
              break;
            }
            emit(SendMessagesToSpeechScreen(
                message: {'root': word, 'word': item.word},
                currentAnimation: state.currentAnimation,
                animationSpeed: state.animationSpeed));
            // await Future.delayed(const Duration(milliseconds: 150));
          }
        } else {
          emit(SendMessagesToSpeechScreen(
              message: {'root': item.root, 'word': item.word},
              currentAnimation: state.currentAnimation,
              animationSpeed: state.animationSpeed));
          // await Future.delayed(const Duration(milliseconds: 150));
        }
      }
    } catch (e) {
      event.progressNotifier.value = 0.0;
      print('Error uploading file for transcription: $e');
      emit(SpeechScreenError(
        message: "Failed to process uploaded file. Please try again.",
        animationSpeed: state.animationSpeed,
      ));
    }
  }

  FutureOr<void> _onStopAllAnimationProcesses(StopAllAnimationProcesses event,
      Emitter<SpeechTranscriptState> emit) async {
    // Set the cancellation flag
    _shouldStopAnimationProcesses = true;

    // Cancel any running timers
    _speechTimer?.cancel();
    _androidSpeechTimer?.cancel();

    // Reset animation state
    isAnimating = false;

    // Clear any pending animation text
    emit(const SpeechTranscriptInitial());
  }

  FutureOr<void> _onUpdateAnimationSpeed(
    UpdateAnimationSpeed event,
    Emitter<SpeechTranscriptState> emit,
  ) async {
    // Clamp the value between 0.5 and 2.0
    animationSpeed = event.value.clamp(0.5, 2.0);

    emit(AnimationSpeedUpdated(
      animationSpeed: animationSpeed,
      currentAnimation: state.currentAnimation,
      isRecordingEnabled: state.isRecordingEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isDataLoading: state.isDataLoading,
      isRecordingStopped: state.isRecordingStopped,
    ));
  }

  // Add new event handler for slider value
  FutureOr<void> _onUpdateSliderValue(
    UpdateSliderValue event,
    Emitter<SpeechTranscriptState> emit,
  ) async {
    // Clamp the value between 0 and 1.5 (matching UnityScreen slider range)
    final clampedValue = event.value.clamp(0.0, 1.5);
    // Convert slider value to animation speed (0-1.5 maps to 0.5-2.0)
    animationSpeed = (clampedValue * (2.0 - 0.5) / 1.5) + 0.5;

    emit(SliderValueUpdated(
      sliderValue: clampedValue,
      currentAnimation: state.currentAnimation,
      isRecordingEnabled: state.isRecordingEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isDataLoading: state.isDataLoading,
      animationSpeed: animationSpeed,
      isRecordingStopped: state.isRecordingStopped,
    ));
  }

  FutureOr<void> _onReplayTranscription(
    ReplayTranscription event,
    Emitter<SpeechTranscriptState> emit,
  ) async {
    // If there are previous transcriptions to replay
    // if (speechTexts.isNotEmpty && speechTextIndex > 0) {
    //   // Reset the current index
    //   currentIndex.value = 0;

    //   // Get the last transcription
    //   final lastTranscription = speechTexts[speechTextIndex - 1];

    //   // Clear current lists
    //   rootWords.clear();
    //   messagesFromUnity.clear();

    //   // Add the words to rootWords
    //   rootWords.addAll(lastTranscription);

    //   // Emit state to start replaying
    //   emit(SendMessagesToSpeechScreen(
    //     message: {'root': lastTranscription[0], 'word': lastTranscription[0]},
    //     currentAnimation: state.currentAnimation,
    //     isRecordingEnabled: state.isRecordingEnabled,
    //     isTranslateButtonDisabled: state.isTranslateButtonDisabled,
    //     isDataLoading: state.isDataLoading,
    //     isRecordingStopped: state.isRecordingStopped,
    //     animationSpeed: state.animationSpeed,
    //   ));
    // }
  }

  Future<FutureOr<void>> _onSendUpdatedSpeech(
      SendUpdatedSpeech event, Emitter<SpeechTranscriptState> emit) async {
    if (speechTexts.length - 1 >= event.index) {
      final speechList = speechTexts[event.index];
      for (var item in speechList) {
        rootWords.add(item);
        print(
            "onSendUpdatedSpeech =>$speechTexts => $rootWords => ${event.index}");
        if (item.contains(',')) {
          final splitWords = item.split(',');

          for (int i = 0; i < splitWords.length; i++) {
            var word = splitWords[i];
            await Future.delayed(const Duration(milliseconds: 150), () {
              emit(SendMessagesToSpeechScreen(
                message: {
                  'root': word,
                  'word': speechWords[event.index].toString()
                },
                currentAnimation: state.currentAnimation,
                animationSpeed: state.animationSpeed,
              ));
            });
          }
        } else {
          emit(SendMessagesToSpeechScreen(
            message: {
              'root': item.trim(),
              'word': speechWords[event.index].toString()
            },
            currentAnimation: state.currentAnimation,
            animationSpeed: state.animationSpeed,
          ));
        }
      }
    }
  }

  FutureOr<void> _onUploadRecording(
      UploadRecording event, Emitter<SpeechTranscriptState> emit) async {
    audioResponseList = [];
    try {
      final path = await event.controller.stop();
      // emit(FileUploadLoadingState(animationSpeed: state.animationSpeed));
      emit(StartRecordingState(
          isRecordingEnabled: true,
          animationSpeed: state.animationSpeed,
          isDataLoading: true));
      final UploadSessionResponse? response = await recordingService
          .createUploadSession(event.path.split('/').last, event.sourceType);
      if (response != null) {
        final status = await uploadAudioFileInChunks(
          audioFile: File(path!),
          sessionId: response.sessionId,
          chunkSize: 1024 * 1024,
          progressNotifier: event.progressNotifier,
        );
        if (status) {
          final transcriptionStatus =
              await recordingService.startAudioChunkTranscription(
            response.sessionId,
            event.sourceType,
          );
          if (transcriptionStatus) {
            await Future.delayed(const Duration(seconds: 10), () async {
              final audioResponse = await recordingService
                  .getAudioTranscription(response.sessionId, 0);
              isChunkUploading = false;
              event.progressNotifier.value = 0.0;

              if (audioResponse != null) {
                audioResponseList.addAll(audioResponse.data);
                final data = audioResponse.data.first;
                isChunkDataProcessing = true;
                for (var segment in data.root) {
                  rootWords.add(segment);
                  if (segment.contains(',')) {
                    final splitWords = segment.split(',');
                    for (int i = 0; i < splitWords.length; i++) {
                      var word = splitWords[i];
                      await Future.delayed(const Duration(milliseconds: 150),
                          () {
                        emit(SendMessagesToSpeechScreen(
                          message: {'root': word, 'word': data.word.toString()},
                          currentAnimation: state.currentAnimation,
                          animationSpeed: state.animationSpeed,
                        ));
                      });
                    }
                  } else {
                    emit(SendMessagesToSpeechScreen(
                      message: {'root': segment, 'word': data.word.toString()},
                      currentAnimation: state.currentAnimation,
                      animationSpeed: state.animationSpeed,
                    ));
                  }
                }
                if (audioResponse.data.last.status == "IN PROGRESS") {
                  audioChunkTimer = Timer.periodic(const Duration(seconds: 10),
                      (timer) async {
                    final audioResponse =
                        await recordingService.getAudioTranscription(
                            response.sessionId,
                            audioResponseList.last.endTime.toInt());
                    if (audioResponse != null) {
                      for (var item in audioResponse.data) {
                        audioResponseList.add(item);
                      }
                      if (audioResponse.data.last.status == "COMPLETED") {
                        timer.cancel();
                      }
                    } else {
                      timer.cancel();
                      emit(SpeechScreenError(
                        message:
                            "Failed to process uploaded file. Please try again.",
                        animationSpeed: state.animationSpeed,
                      ));
                      emit(const SpeechTranscriptInitial());
                    }
                  });
                }
              } else {
                emit(SpeechScreenError(
                  message: "Failed to process uploaded file. Please try again.",
                  animationSpeed: state.animationSpeed,
                ));
                emit(const SpeechTranscriptInitial());
              }
            });
          } else {
            isChunkUploading = false;
            emit(SpeechScreenError(
              message: "Failed to start transcription. Please try again.",
              animationSpeed: state.animationSpeed,
            ));
          }
        } else {
          emit(SpeechScreenError(
            message: "Failed to process uploaded file. Please try again.",
            animationSpeed: state.animationSpeed,
          ));
        }
      }
    } catch (e) {
      isChunkUploading = false;
      emit(SpeechScreenError(
        message: "Failed to process uploaded file. Please try again.",
        animationSpeed: state.animationSpeed,
      ));
    }
  }

  FutureOr<void> _onUploadChunkData(
      UploadChunkData event, Emitter<SpeechTranscriptState> emit) async {
    audioResponseList = [];
    try {
      isChunkUploading = true;
      emit(FileUploadLoadingState(animationSpeed: state.animationSpeed));
      final UploadSessionResponse? response = await recordingService
          .createUploadSession(event.file.name, event.sourceType);
      if (response != null) {
        final status = await uploadAudioFileInChunks(
          audioFile: File(event.file.path!),
          sessionId: response.sessionId,
          chunkSize: 1024 * 1024,
          progressNotifier: event.progressNotifier,
        );
        if (status) {
          final transcriptionStatus =
              await recordingService.startAudioChunkTranscription(
            response.sessionId,
            event.sourceType,
          );
          if (transcriptionStatus) {
            await Future.delayed(const Duration(seconds: 10), () async {
              final audioResponse = await recordingService
                  .getAudioTranscription(response.sessionId, 0);
              isChunkUploading = false;
              event.progressNotifier.value = 0.0;

              if (audioResponse != null) {
                audioResponseList.addAll(audioResponse.data);
                final data = audioResponse.data.first;
                isChunkDataProcessing = true;
                for (var segment in data.root) {
                  rootWords.add(segment);
                  if (segment.contains(',')) {
                    final splitWords = segment.split(',');
                    for (int i = 0; i < splitWords.length; i++) {
                      var word = splitWords[i];
                      await Future.delayed(const Duration(milliseconds: 150),
                          () {
                        emit(SendMessagesToSpeechScreen(
                          message: {'root': word, 'word': data.word.toString()},
                          currentAnimation: state.currentAnimation,
                          animationSpeed: state.animationSpeed,
                        ));
                      });
                    }
                  } else {
                    emit(SendMessagesToSpeechScreen(
                      message: {'root': segment, 'word': data.word.toString()},
                      currentAnimation: state.currentAnimation,
                      animationSpeed: state.animationSpeed,
                    ));
                  }
                }
                if (audioResponse.data.last.status == "IN PROGRESS") {
                  audioChunkTimer = Timer.periodic(const Duration(seconds: 10),
                      (timer) async {
                    final audioResponse =
                        await recordingService.getAudioTranscription(
                            response.sessionId,
                            audioResponseList.last.endTime.toInt());
                    if (audioResponse != null) {
                      for (var item in audioResponse.data) {
                        audioResponseList.add(item);
                      }
                      if (audioResponse.data.last.status == "COMPLETED") {
                        timer.cancel();
                      }
                    } else {
                      timer.cancel();
                      emit(SpeechScreenError(
                        message:
                            "Failed to process uploaded file. Please try again.",
                        animationSpeed: state.animationSpeed,
                      ));
                      emit(const SpeechTranscriptInitial());
                    }
                  });
                }
              } else {
                emit(SpeechScreenError(
                  message: "Failed to process uploaded file. Please try again.",
                  animationSpeed: state.animationSpeed,
                ));
                emit(const SpeechTranscriptInitial());
              }
            });
          } else {
            isChunkUploading = false;
            emit(SpeechScreenError(
              message: "Failed to start transcription. Please try again.",
              animationSpeed: state.animationSpeed,
            ));
          }
        } else {
          emit(SpeechScreenError(
            message: "Failed to process uploaded file. Please try again.",
            animationSpeed: state.animationSpeed,
          ));
        }
      }
    } catch (e) {
      isChunkUploading = false;
      emit(SpeechScreenError(
        message: "Failed to process uploaded file. Please try again.",
        animationSpeed: state.animationSpeed,
      ));
    }
  }

  Future<bool> uploadAudioFileInChunks({
    required File audioFile,
    required int sessionId,
    required int chunkSize, // e.g. 512 * 1024 for 512 KB
    required ValueNotifier<double> progressNotifier,
  }) async {
    final totalSize = await audioFile.length();
    final totalChunks = (totalSize / chunkSize).ceil();
    final raf = audioFile.openSync(mode: FileMode.read);

    int chunkNumber = 1; // Start from 1
    int uploadedBytes = 0;
    const int maxRetries = 3;

    try {
      while (chunkNumber <= totalChunks) {
        final offset = (chunkNumber - 1) * chunkSize;
        final remaining = totalSize - offset;
        final size = remaining >= chunkSize ? chunkSize : remaining;

        final chunkFile = File('${audioFile.path}_chunk_$chunkNumber.tmp');
        final chunkBytes = raf.readSync(size);
        chunkFile.writeAsBytesSync(chunkBytes);

        bool uploaded = false;
        int retryCount = 0;

        while (!uploaded && retryCount < maxRetries) {
          final response = await recordingService.uploadAudioChunk(
            audioFile: chunkFile,
            sessionId: sessionId,
            chunkNumber: chunkNumber,
            totalChunks: totalChunks,
          );

          if (response != null) {
            uploadedBytes += size;
            double progress = uploadedBytes / totalSize;
            progressNotifier.value = progress;
            uploaded = true;
          } else {
            retryCount++;
            await Future.delayed(const Duration(seconds: 2));
          }
        }

        if (!uploaded) {
          await chunkFile.delete();
          raf.closeSync();
          return false; //
        }

        await chunkFile.delete();
        chunkNumber++;
      }

      raf.closeSync();

      if (progressNotifier.value == 1.0) {
        return true; //
      } else {
        return false; // Edge case: progress not fully 1.0
      }
    } catch (e, stack) {
      debugPrintStack(stackTrace: stack);
      raf.closeSync();
      return false; //
    }
  }

  Future<FutureOr<void>> _onSendAudioSegment(
      SendAudioSegment event, Emitter<SpeechTranscriptState> emit) async {
    if (audioResponseList.length - 1 >= event.index) {
      final audioSegment = audioResponseList[event.index];
      for (var item in audioSegment.root) {
        rootWords.add(item);
        if (item.contains(',')) {
          final splitWords = item.split(',');
          for (int i = 0; i < splitWords.length; i++) {
            var word = splitWords[i];
            await Future.delayed(const Duration(milliseconds: 150), () {
              emit(SendMessagesToSpeechScreen(
                message: {'root': word, 'word': audioSegment.word.toString()},
                currentAnimation: state.currentAnimation,
                animationSpeed: state.animationSpeed,
              ));
            });
          }
        } else {
          emit(SendMessagesToSpeechScreen(
            message: {'root': item, 'word': audioSegment.word.toString()},
            currentAnimation: state.currentAnimation,
            animationSpeed: state.animationSpeed,
          ));
        }
      }
    }
  }

  FutureOr<void> _onReplayRecording(
      ReplayRecording event, Emitter<SpeechTranscriptState> emit) {
    if (latsRecordingList.isNotEmpty) {
      for (var item in latsRecordingList) {
        final rootWord = item.root ?? "";
        // rootWords.add(rootWord);
        if (rootWord.contains(',')) {
          final splitWords = rootWord.split(',');
          for (var word in splitWords) {
            emit(SendMessagesToSpeechScreen(
              message: {'root': word, 'word': item.word},
              currentAnimation: state.currentAnimation,
              animationSpeed: state.animationSpeed,
            ));
          }
        } else {
          emit(SendMessagesToSpeechScreen(
            message: {'root': item.root, 'word': item.word},
            currentAnimation: state.currentAnimation,
            animationSpeed: state.animationSpeed,
          ));
        }
      }
    }
  }

  FutureOr<void> _onReplayFileUpload(
      ReplayFileUpload event, Emitter<SpeechTranscriptState> emit) {
    if (lastFileUploadList.isNotEmpty) {
      for (var item in lastFileUploadList) {
        // Check if we should stop the animation processes
        if (_shouldStopAnimationProcesses) {
          break;
        }

        final rootWord = item.root ?? "";
        if (rootWord.contains(',')) {
          final splitWords = rootWord.split(',');
          for (var word in splitWords) {
            // Check again before emitting
            if (_shouldStopAnimationProcesses) {
              break;
            }
            emit(SendMessagesToSpeechScreen(
                message: {'root': word, 'word': item.word},
                currentAnimation: state.currentAnimation,
                animationSpeed: state.animationSpeed));
            // await Future.delayed(const Duration(milliseconds: 150));
          }
        } else {
          emit(SendMessagesToSpeechScreen(
              message: {'root': item.root, 'word': item.word},
              currentAnimation: state.currentAnimation,
              animationSpeed: state.animationSpeed));
          // await Future.delayed(const Duration(milliseconds: 150));
        }
      }
    }
  }

  FutureOr<void> _onSendSessionAlert(
      SendSessionAlert event, Emitter<SpeechTranscriptState> emit) {
    emit(SpeechTranscriptInitial(
      currentAnimation: state.currentAnimation,
      isRecordingEnabled: state.isRecordingEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isDataLoading: state.isDataLoading,
      animationSpeed: state.animationSpeed,
    ));
    emit(ShowSessionAlert(alertCount: event.alertCount));
  }
}
